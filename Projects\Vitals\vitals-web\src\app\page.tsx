import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Vitals</h1>
              <span className="ml-2 text-sm text-gray-500">by Afyalink</span>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="/facilities" className="text-gray-700 hover:text-gray-900">Facilities</Link>
              <Link href="/costs" className="text-gray-700 hover:text-gray-900">Costs</Link>
              <Link href="/nhima" className="text-gray-700 hover:text-gray-900">NHIMA</Link>
              <Link href="/care-workers" className="text-gray-700 hover:text-gray-900">Care Workers</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Your Complete Health Platform for Zambia
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Find NHIMA-covered hospitals, clinics, and pharmacies. Get accurate healthcare costs,
            facility information, and book trusted care workers across Zambia.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/facilities">Find Healthcare Facilities</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/nhima">Check NHIMA Coverage</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Quick Search Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            What are you looking for?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">🏥 Healthcare Facilities</h3>
              <p className="text-gray-600 mb-4">Find hospitals, clinics, and pharmacies in your area with NHIMA coverage information.</p>
              <Link href="/facilities" className="text-black font-medium hover:underline">
                Browse Facilities →
              </Link>
            </div>
            <div className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">💰 Medical Costs</h3>
              <p className="text-gray-600 mb-4">Get transparent pricing for medical procedures and services across Zambia.</p>
              <Link href="/costs" className="text-black font-medium hover:underline">
                Check Costs →
              </Link>
            </div>
            <div className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">👩‍⚕️ Care Workers</h3>
              <p className="text-gray-600 mb-4">Book trusted, verified care workers for home healthcare services.</p>
              <Link href="/care-workers" className="text-black font-medium hover:underline">
                Book Care Worker →
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Searches */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Popular Searches
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              "NHIMA Hospitals Lusaka",
              "MRI Scan Costs",
              "Pharmacies Kitwe",
              "Blood Test Prices",
              "NHIMA Clinics Ndola",
              "X-Ray Costs",
              "Dental Clinics",
              "Lab Test Prices"
            ].map((search) => (
              <Link
                key={search}
                href={`/search?q=${encodeURIComponent(search)}`}
                className="bg-gray-100 text-gray-700 px-4 py-3 rounded-lg text-center hover:bg-gray-200 transition-colors"
              >
                {search}
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">Vitals</h3>
              <p className="text-gray-400">Your complete health platform for Zambia. Powered by Afyalink.</p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/facilities" className="hover:text-white">Healthcare Facilities</Link></li>
                <li><Link href="/costs" className="hover:text-white">Medical Costs</Link></li>
                <li><Link href="/nhima" className="hover:text-white">NHIMA Coverage</Link></li>
                <li><Link href="/care-workers" className="hover:text-white">Care Workers</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Resources</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/guides" className="hover:text-white">Health Guides</Link></li>
                <li><Link href="/about" className="hover:text-white">About Us</Link></li>
                <li><Link href="/contact" className="hover:text-white">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Contact</h4>
              <p className="text-gray-400">Lusaka, Zambia</p>
              <p className="text-gray-400"><EMAIL></p>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Afyalink. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
