import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Vitals - Your Complete Health Platform | Zambian Healthcare Guide",
  description: "Find NHIMA-covered hospitals, clinics, and pharmacies in Zambia. Get accurate healthcare costs, facility information, and book trusted care workers. Your complete guide to healthcare in Lusaka, Kitwe, and across Zambia.",
  keywords: "NHIMA hospitals Zambia, healthcare costs Lusaka, Zambian clinics, NHIMA coverage, healthcare facilities Kitwe, medical costs Zambia, care workers Zambia",
  openGraph: {
    title: "Vitals - Your Complete Health Platform",
    description: "Find NHIMA-covered healthcare facilities and accurate medical costs across Zambia",
    type: "website",
    locale: "en_ZM",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased bg-background text-text-primary`}>
        {children}
      </body>
    </html>
  );
}
