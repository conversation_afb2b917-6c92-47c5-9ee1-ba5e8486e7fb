@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* AfyaLink Color Palette */
  --primary: #2E7D32; /* Trust Green */
  --secondary: #1E40AF; /* Professional Blue */
  --accent-cta: #F59E0B; /* Action Orange */
  --background: #F8FAFC; /* Light Neutral */
  --text-primary: #1E293B; /* Dark Slate */
  --text-muted: #64748B; /* Medium Slate */
  --feedback-success: #16A34A; /* Success Green */
  --feedback-warning: #FACC15; /* Warning Amber */
  --feedback-error: #DC2626; /* Error Red */
  --border: #CBD5E1; /* Border color */
}

@theme inline {
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent-cta: var(--accent-cta);
  --color-background: var(--background);
  --color-text-primary: var(--text-primary);
  --color-text-muted: var(--text-muted);
  --color-feedback-success: var(--feedback-success);
  --color-feedback-warning: var(--feedback-warning);
  --color-feedback-error: var(--feedback-error);
  --color-border: var(--border);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
}

/* Component Classes for AfyaLink */
.btn-primary {
  @apply bg-accent-cta text-white font-bold py-3 px-6 rounded-md hover:bg-amber-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-cta transition-colors;
}

.btn-secondary {
  @apply bg-transparent border border-secondary text-secondary font-bold py-3 px-6 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors;
}

.content-card {
  @apply bg-white border border-border rounded-lg p-6 shadow-card;
}

.badge-covered {
  @apply inline-flex items-center rounded-full bg-feedback-success/10 px-3 py-1 text-sm font-medium text-feedback-success;
}

.badge-not-covered {
  @apply inline-flex items-center rounded-full bg-feedback-error/10 px-3 py-1 text-sm font-medium text-feedback-error;
}

.badge-copayment {
  @apply inline-flex items-center rounded-full bg-feedback-warning/10 px-3 py-1 text-sm font-medium text-yellow-800;
}
