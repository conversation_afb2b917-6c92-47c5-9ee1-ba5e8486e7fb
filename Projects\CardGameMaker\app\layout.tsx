import type { Metada<PERSON> } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Card Game Maker - Question/Answer/Shot Game",
  description: "Multiplayer card game for fun question/answer/take shot gameplay",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
