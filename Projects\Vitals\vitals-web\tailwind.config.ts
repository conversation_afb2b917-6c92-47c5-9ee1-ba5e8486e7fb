import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: '#2E7D32', // Trust Green
        secondary: '#1E40AF', // Professional Blue
        'accent-cta': '#F59E0B', // Action Orange
        background: '#F8FAFC', // Light Neutral
        'text-primary': '#1E293B', // Dark Slate for text
        'text-muted': '#64748B', // Medium Slate for secondary text
        'feedback-success': '#16A34A', // Green for success, "Covered"
        'feedback-warning': '#FACC15', // Amber for warnings, "Co-payment"
        'feedback-error': '#DC2626',   // Red for errors, "Not Covered"
        border: '#CBD5E1', // For input borders, cards
      },
      fontFamily: {
        // Set Inter as the default sans-serif font
        sans: ['Inter', 'sans-serif'],
      },
      borderRadius: {
        'lg': '0.75rem',
        'md': '0.5rem',
        'sm': '0.25rem',
      },
      // Optional: Add some subtle box-shadows for cards
      boxShadow: {
        'card': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'), // Highly recommended for better form styling
  ],
}
export default config
