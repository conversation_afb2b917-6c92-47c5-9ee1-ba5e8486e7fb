# Task Master - Global Rules

## Core Rules
- `__rules`: This provides rules you'll need to follow in each workspace
- `__rules` in parent folders apply to their children!!
- `__logs` in each folder represent activities taken in the space and should be appended
- `__memo`: These are notes taken from memos daily and should be rechecked for diffs in every run
- `__home`: These are the about of the folder, it should contain a brief and links to the child __home files with their folder names
- If there are no `__rules` or `__logs` in the a folder then create them even if they are empty
- Any user correction should be appended to the `__rules` file as a new `__rule`

## CRITICAL DOMAIN KNOWLEDGE RULE
- **MANDATORY**: Before ANY action, ALWAYS read ALL relevant __rules.md and __logs.md files in:
  1. Current working directory
  2. Project-specific directory (e.g., Projects/Agency Work/Afyalink/__rules.md)
  3. Parent directory rules that apply
- **DEPLOYMENT COMPLIANCE**: If project has deployment requirements, MUST deploy immediately after code changes
- **DOMAIN KNOWLEDGE FAILURE**: Failure to check domain knowledge files is a critical error that must be corrected

## AGENT OPERATIONAL RULES

### HOME DIRECTORY AWARENESS
- **AGENT HOME**: C:\Users\<USER>\Documents\V\DaVAWT is the agent's permanent home directory
- **RETURN REQUIREMENT**: MUST return to home directory after completing any external tasks
- **DIRECTORY CONTEXT**: Always maintain awareness of current directory and its purpose
- **PATH TRACKING**: Keep track of directory changes and ensure return path is clear

### TASK COMPLETION PROTOCOL
- **COMPLETE ALL TASKS**: When given multiple tasks, complete ALL tasks before finishing
- **TASK MEMORY**: Maintain memory of all assigned tasks throughout the session
- **COMPLETION VERIFICATION**: Check off each completed task explicitly
- **NO ABANDONMENT**: Never abandon incomplete tasks without explicit user permission

### ROLE-BASED RESPONSIBILITIES
- **MULTI-ROLE AWARENESS**: If operating with multiple roles/responsibilities, complete ALL role tasks
- **ROLE PRIORITY**: Follow role priority order as defined in team structure
- **CROSS-ROLE COORDINATION**: Ensure tasks across different roles are coordinated
- **ROLE SWITCHING**: When switching roles, explicitly acknowledge the role change

### SESSION MANAGEMENT
- **SESSION CONTINUITY**: Maintain context of ongoing tasks throughout the session
- **TASK STACK**: Keep a mental stack of all pending tasks
- **COMPLETION SUMMARY**: At end of session, provide summary of all completed and pending tasks
- **HANDOFF PREPARATION**: Prepare clear handoff documentation for next session

### OPERATIONAL SEQUENCE
1. **START**: Read domain knowledge (__rules.md, __logs.md, __memo.md)
2. **PLAN**: Identify all tasks and roles assigned
3. **EXECUTE**: Complete tasks while maintaining home directory awareness
4. **TRACK**: Update logs and progress throughout
5. **RETURN**: Return to home directory
6. **VERIFY**: Confirm all tasks completed
7. **LOG**: Update completion status in appropriate log files
- The `__memo` will be created in the root each day to which it needs to be related to its corresponding project
- The `__memo` are unstructured notes related to real world feedback that feeds into both projects and tasks (they are also daily notes essentially)
- Only refer to latest results as the best option for a response
- Do not waffle in the response, just respond with meaningful output of what was done. The User does not care about the AI personality
- Use #tags as much as possible and add them to a separate section of this document to keep them in order.
- #tags are global for now
- `__memo` populate the daily notes in a more structured way
- `__online` represents and online source

## Task Master Automation Rules
- **AUTO-UPDATE RULE**: On every run, update Performance/__home.md and Team/__home.md with current project status, team utilization, and KPIs
- **MEMO INTEGRATION**: Always check __memo.md for new information and integrate into relevant project files and team assignments
- **FIRST RUN**: If Projects folders are empty, auto-populate from __start.md structure
- **TASK LINKING**: Daily tasks must link to corresponding project task files
- **TASK ROLLOVER**: Incomplete tasks roll over to next day or get cancelled based on memo/user input
- **ARCHIVE SYSTEM**: Completed projects and tasks automatically archived with proper linking

## Agent Commands
- **`__agent:`** - Inline command for specific instructions within any file
- **`__reset`** - Create backup in ../Backups/ and reset workspace to template
- **Examples**:
  - `__agent: Update this project status to completed`
  - `__agent: Archive this task and create follow-up`
  - `__agent: Generate weekly performance report`
- `__folder` this is for folder folder locations
- `__folder` need to be thoroughly searched for logs and readme and git for status of the project to populate the project and tasks
## Styling Rules
- **`__style`**: All applications must use components and styling from these repositories:
  - **Shadcn UI**: https://github.com/shadcn-ui/ui - Use for core UI components, forms, and layouts
  - **Framer Ground**: https://github.com/code-env/framer-ground - Use for animations and advanced interactions
- **Default Color Scheme**: Keep applications black and white by default unless specific colors are chosen to avoid hideous designs
- **Component Priority**: Always check Shadcn and Framer Ground docs thoroughly before creating custom components
- **Design System**: Follow established design patterns from these repositories for consistency
- **Documentation**: Study both repositories' documentation in detail to implement best practices

## #tags

### Project Status Tags
- #started - Project has begun
- #notstarted - Project not yet initiated
- #completed - Project finished
- #blocked - Project has blocking issues
- #overdue - Project past deadline

### Payment Status Tags
- #paid - Fully paid
- #halfpaid - Partially paid
- #unpaid - No payment received

### Tracking Tags
- #tracked - Project is being tracked
- #untracked - Project not being tracked
- #gittracked - Version controlled

### Priority Tags
- #priority - High priority item
- #dailytasks - Daily task items

### Team Tags
- #team - Team-related activities
- #collaboration - Collaborative work
- #roles - Role definitions

### Organization Tags
- #workspace - Workspace organization
- #organization - General organization
- #taskmanagement - Task management activities
- #projects - Project-related items
- #performance - Performance tracking
- #kpi - Key Performance Indicators