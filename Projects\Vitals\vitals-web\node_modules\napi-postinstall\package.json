{"name": "napi-postinstall", "version": "0.2.5", "type": "commonjs", "description": "The `postinstall` script helper for handling native bindings in legacy `npm` versions", "repository": "git+https://github.com/un-ts/napi-postinstall.git", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stG.me)", "funding": "https://opencollective.com/napi-postinstall", "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "bin": "./lib/cli.js", "main": "./lib/index.js", "types": "./lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "files": ["lib", "!**/*.tsbuildinfo"], "packageManager": "npm@11.4.2+sha512.f90c1ec8b207b625d6edb6693aef23dacb39c38e4217fe8c46a973f119cab392ac0de23fe3f07e583188dae9fd9108b3845ad6f525b598742bd060ebad60bff3"}