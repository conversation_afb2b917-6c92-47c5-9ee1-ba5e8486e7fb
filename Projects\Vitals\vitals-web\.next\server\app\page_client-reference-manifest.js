globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"5656","name":"*","chunks":[],"async":false}},"6874":{"*":{"id":"5814","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":1666,"name":"*","chunks":["177","static/chunks/app/layout-fc72ca4d2ce6f147.js"],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\src\\app\\globals.css":{"id":347,"name":"*","chunks":["177","static/chunks/app/layout-fc72ca4d2ce6f147.js"],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":6874,"name":"*","chunks":["974","static/chunks/app/page-3b434c3e43bf493c.js"],"async":false},"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":6874,"name":"*","chunks":["974","static/chunks/app/page-3b434c3e43bf493c.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\src\\":[],"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\src\\app\\layout":[{"inlined":false,"path":"static/css/5b576904c612405e.css"}],"C:\\Users\\<USER>\\Documents\\V\\DaVAWT\\Projects\\Vitals\\vitals-web\\src\\app\\page":[]},"rscModuleMapping":{"347":{"*":{"id":"1135","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"8170","name":"*","chunks":[],"async":false}},"6874":{"*":{"id":"4536","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}