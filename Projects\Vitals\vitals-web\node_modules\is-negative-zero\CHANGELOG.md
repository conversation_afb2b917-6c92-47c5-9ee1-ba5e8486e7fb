# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.0.3](https://github.com/inspect-js/is-negative-zero/compare/v2.0.2...v2.0.3) - 2024-02-19

### Commits

- add types [`e28f0d5`](https://github.com/inspect-js/is-negative-zero/commit/e28f0d59cffc61bd3d41c10563105e8adb868e59)
- [meta] use `npmignore` to autogenerate an npmignore file [`f68ec13`](https://github.com/inspect-js/is-negative-zero/commit/f68ec13c1d04a2ef1e1c72506ae9c2ac0567bcdd)
- [<PERSON>] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`70abff7`](https://github.com/inspect-js/is-negative-zero/commit/70abff788bb0156f300a09c2f5cc7d30d5618184)
- [actions] update rebase action to use reusable workflow [`6e1356e`](https://github.com/inspect-js/is-negative-zero/commit/6e1356e9fc5fee1bcae20b365c7d71b14ecf876d)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `npmignore`, `tape` [`c00d4ab`](https://github.com/inspect-js/is-negative-zero/commit/c00d4ab5c381d22ac3e6d89180b3e012c479f13f)
- [meta] add `sideEffects` flag [`9c45539`](https://github.com/inspect-js/is-negative-zero/commit/9c455398988d2db940fe6644541d09d510b6661f)

## [v2.0.2](https://github.com/inspect-js/is-negative-zero/compare/v2.0.1...v2.0.2) - 2021-12-10

### Commits

- [actions] reuse common workflows [`ece923d`](https://github.com/inspect-js/is-negative-zero/commit/ece923d6b50820b7832150957774047da43d1743)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`3a26f43`](https://github.com/inspect-js/is-negative-zero/commit/3a26f435434bbfb8a254ddd32d6079bf50263121)
- [meta] do not publish workflow files [`2cea0c2`](https://github.com/inspect-js/is-negative-zero/commit/2cea0c20c7f8b167bb6064f916f3285642bf5ab1)
- [readme] add github actions/codecov badges; update URLs [`0c0be3e`](https://github.com/inspect-js/is-negative-zero/commit/0c0be3eb148ccf9d764e8ed515ef0e1d0ffeed9d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`a93d16e`](https://github.com/inspect-js/is-negative-zero/commit/a93d16eddf46cad6bc9b71a92968ce758e6f4ea4)
- [meta] create FUNDING.yml [`b4f425e`](https://github.com/inspect-js/is-negative-zero/commit/b4f425e49edb8c4aa1efa6ba34c2d5c1150b2a26)
- [actions] update codecov uploader [`7999db3`](https://github.com/inspect-js/is-negative-zero/commit/7999db3bff3f0f9cc729529940b9077a586822e2)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `safe-publish-latest`, `tape` [`140e4d9`](https://github.com/inspect-js/is-negative-zero/commit/140e4d95eda9ad821608c28a5f705d84a906096d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`23a8b6d`](https://github.com/inspect-js/is-negative-zero/commit/23a8b6d257cea0b8a608b2319739562ecbc6ba75)
- [readme] add actions and codecov badges [`fe92126`](https://github.com/inspect-js/is-negative-zero/commit/fe9212634346ced7a45905d958cb85d129e4c10b)
- [readme] fix repo URLs [`50c428e`](https://github.com/inspect-js/is-negative-zero/commit/50c428e423e5861a6c231440b8b3e746cbf6230f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`688155f`](https://github.com/inspect-js/is-negative-zero/commit/688155ff0214da72cbafa4c438c3b9629265d82b)
- [meta] use `prepublishOnly` script for npm 7+ [`83171f9`](https://github.com/inspect-js/is-negative-zero/commit/83171f9131aed266f475d7a3283d9c2fc77e1436)
- [actions] update workflows [`e9823db`](https://github.com/inspect-js/is-negative-zero/commit/e9823db3054887d8bb5b3f2c8f698b93cdce7d82)

## [v2.0.1](https://github.com/inspect-js/is-negative-zero/compare/v2.0.0...v2.0.1) - 2020-12-04

### Commits

- [Tests] use shared travis-ci configs [`5b92482`](https://github.com/inspect-js/is-negative-zero/commit/5b92482ed26e55e1aafcc6b6310d279958af8204)
- [Tests] up to `node` `v11.7`, `v10.15`, `v9.11`, `v8.15`, `v7.10`, `v6.16`, `v5.12`, `v4.9`; use `nvm install-latest-npm`; fix test scripts [`0f5d2f8`](https://github.com/inspect-js/is-negative-zero/commit/0f5d2f85ea7fe83de47f39b6b35e489b866d88a7)
- [Tests] migrate tests to Github Actions [`b80f05a`](https://github.com/inspect-js/is-negative-zero/commit/b80f05adb11a6a3232860fb50272b101aacb504f)
- [Tests] remove `jscs` [`7ccaf41`](https://github.com/inspect-js/is-negative-zero/commit/7ccaf4100281b614d61d7c9122e6f87943a89295)
- [meta] add missing changelog [`992bdde`](https://github.com/inspect-js/is-negative-zero/commit/992bddee362cbae71f2cdfd8666f4774b252412e)
- [readme] fix repo URLs; remove defunct badges [`80fd18d`](https://github.com/inspect-js/is-negative-zero/commit/80fd18d2b0191321afe0e1b572200e4c025eb664)
- [Tests] run `nyc` on all tests [`df26f14`](https://github.com/inspect-js/is-negative-zero/commit/df26f14b0b854d82b0d3ca7b4949811c9f151357)
- Update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`d7723aa`](https://github.com/inspect-js/is-negative-zero/commit/d7723aa70e5b478adc36d98e1338abe741c1906a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`9fdaabe`](https://github.com/inspect-js/is-negative-zero/commit/9fdaabecfdb25e6e860e5007a91b60ee0f20734f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape` [`f07eeb2`](https://github.com/inspect-js/is-negative-zero/commit/f07eeb2740037c53f270e95d2f62edc051cafc56)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`bd5c751`](https://github.com/inspect-js/is-negative-zero/commit/bd5c751fa4850ba8726dc1c197ed6c843a227b05)
- [actions] add automatic rebasing / merge commit blocking [`5666a91`](https://github.com/inspect-js/is-negative-zero/commit/5666a917db6bdcee63c0a3e28e5e281359975abc)
- [meta] add `auto-changelog` [`f70fb2b`](https://github.com/inspect-js/is-negative-zero/commit/f70fb2b5b9ea53dc52729310717553648292189e)
- [actions] add "Allow Edits" workflow [`2b040a8`](https://github.com/inspect-js/is-negative-zero/commit/2b040a87d362f17d8cab2b0d48058b80e426ad4e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `safe-publish-latest` [`09e2e53`](https://github.com/inspect-js/is-negative-zero/commit/09e2e537390225c1d1a6912be64267eaec6ea367)
- [Tests] use `npm audit` instead of `nsp` [`7df2669`](https://github.com/inspect-js/is-negative-zero/commit/7df2669013ac9328d424e9d8c82a53a0458f0888)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`4ff97c5`](https://github.com/inspect-js/is-negative-zero/commit/4ff97c5891c7a241a91c03fb54bd83e78570ef22)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`9e8cb7b`](https://github.com/inspect-js/is-negative-zero/commit/9e8cb7bca46d325ecf202187c0fde7da8722bcab)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config`, `nsp` [`70b9888`](https://github.com/inspect-js/is-negative-zero/commit/70b988802a99c84ab7eb8da287bb8ff0efc5c055)
- [Dev Deps] update `jscs` [`59d0c42`](https://github.com/inspect-js/is-negative-zero/commit/59d0c42131020b74e68fd444798b9a3bf247fb2d)
- Add `npm run security` [`eb418ed`](https://github.com/inspect-js/is-negative-zero/commit/eb418ed7e79216808c206388fbd360cc7a75655f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`86a758d`](https://github.com/inspect-js/is-negative-zero/commit/86a758d42eb7d17a18f7f584c337d8820b842758)
- Only apps should have lockfiles [`a0ab621`](https://github.com/inspect-js/is-negative-zero/commit/a0ab6215590bf6adb3eaf1ff9e7c036d72e807ec)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`5c51349`](https://github.com/inspect-js/is-negative-zero/commit/5c513498fc5a8b2fd06f5e0c1b38b8e93c3477ac)
- [meta] add `funding` field [`1d0b2f4`](https://github.com/inspect-js/is-negative-zero/commit/1d0b2f43bf5bf75176859a440346b3e338ee510e)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`9b12367`](https://github.com/inspect-js/is-negative-zero/commit/9b12367706f1c269a3df406c8e2c211558671a15)
- [Dev Deps] update `auto-changelog`, `tape` [`6d98b8d`](https://github.com/inspect-js/is-negative-zero/commit/6d98b8d1f512c3844d4062215c793070084d1164)
- [Dev Deps] Update `tape`, `eslint` [`a258cdb`](https://github.com/inspect-js/is-negative-zero/commit/a258cdb86691725482d1d43a1f9e7953c3c6733f)
- [Dev Deps] update `auto-changelog`; add `aud` [`2ca2afb`](https://github.com/inspect-js/is-negative-zero/commit/2ca2afb9efef4ebc8b3c19046ab1ab3ad516ea9a)
- Test up to `io.js` `v3.0` [`1254ae8`](https://github.com/inspect-js/is-negative-zero/commit/1254ae80b7706616331ac914654d7a17bff31039)
- [Dev Deps] update `auto-changelog` [`4b54722`](https://github.com/inspect-js/is-negative-zero/commit/4b547228fceaae8f9eccabc9ad8a49046492348d)
- [Tests] only audit prod deps [`86d298b`](https://github.com/inspect-js/is-negative-zero/commit/86d298b56db90f81617ee5d942476eda34afb374)
- [Dev Deps] update `tape` [`3a47e27`](https://github.com/inspect-js/is-negative-zero/commit/3a47e2730f889539f666ef0eb09a93fb9c80bfd1)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`128d9bd`](https://github.com/inspect-js/is-negative-zero/commit/128d9bd4c12385fb5e478ac3dd3138fa4360a777)

## [v2.0.0](https://github.com/inspect-js/is-negative-zero/compare/v1.0.0...v2.0.0) - 2015-07-24

### Commits

- Update `tape`, `eslint`; use my personal shared `eslint` config. [`648d002`](https://github.com/inspect-js/is-negative-zero/commit/648d0029b177886428a11b07307f233ae2d3175b)
- Add `npm run eslint` [`5a52d80`](https://github.com/inspect-js/is-negative-zero/commit/5a52d80ab052e377044b9b181991a32afaaa3090)
- Using my standard jscs.json file [`5a667d9`](https://github.com/inspect-js/is-negative-zero/commit/5a667d9f8b7402ca3bd134080cd4435b5c7f1462)
- Adding `npm run lint` [`9a85ed9`](https://github.com/inspect-js/is-negative-zero/commit/9a85ed934da65d8733a38bf6ad3c89fd62115194)
- Update `tape`, `covert`, `jscs` [`c6cd3a6`](https://github.com/inspect-js/is-negative-zero/commit/c6cd3a64ea5b98100e10537549f50a9eeadc30ec)
- Update `eslint` [`e9c9b6e`](https://github.com/inspect-js/is-negative-zero/commit/e9c9b6e9623f021b7f3ae4091bf9ea2571ab02b4)
- Test on latest `io.js` [`2f7c8a9`](https://github.com/inspect-js/is-negative-zero/commit/2f7c8a9d174066400c072841d7bcf02cf90f8ebf)
- Adding license and downloads badges [`717087a`](https://github.com/inspect-js/is-negative-zero/commit/717087a013b4cfc9dc7847d3d4a64faf19341be4)
- Remove Number type coercion. [`481295d`](https://github.com/inspect-js/is-negative-zero/commit/481295dbd09dbf81d196dc77382f1b92f534de3f)
- Test up to `io.js` `v2.1` [`139a84a`](https://github.com/inspect-js/is-negative-zero/commit/139a84a3dbdec29682044c6e7ac884a7382ae6e1)
- Update `eslint` [`2f5fbfb`](https://github.com/inspect-js/is-negative-zero/commit/2f5fbfbc436ccd8676fc36fcd9f0edcdda33a1e7)
- Update `eslint` [`53cb4c5`](https://github.com/inspect-js/is-negative-zero/commit/53cb4c5eccecdf2d874e78e5c38cca762ed76a9d)
- Test on `io.js` `v2.2` [`98a1824`](https://github.com/inspect-js/is-negative-zero/commit/98a1824c86366f8a03cf303f46acecd67409f94b)
- All grade A-supported `node`/`iojs` versions now ship with an `npm` that understands `^`. [`772d6cd`](https://github.com/inspect-js/is-negative-zero/commit/772d6cdf397e6fc1b26e5ed5f279d07a5ead6df8)
- Run `travis-ci` tests on `iojs` and `node` v0.12; speed up builds; allow 0.8 failures. [`3e6147e`](https://github.com/inspect-js/is-negative-zero/commit/3e6147ebb5ecdfda4a2bffa441572fe017c7bb73)
- Use SVG badges instead of PNG [`d986cb4`](https://github.com/inspect-js/is-negative-zero/commit/d986cb450e5bd4f373041255b69e18f47c252a1e)
- Update `tape`, `jscs` [`9f9d7e7`](https://github.com/inspect-js/is-negative-zero/commit/9f9d7e751bcf4ceeed8e36e75e31d29c76326b3f)
- Update `jscs` [`079eaf6`](https://github.com/inspect-js/is-negative-zero/commit/079eaf699d53e7e32c54e5233259a00f5f494d9a)
- Update `tape`, `jscs` [`cffe3fc`](https://github.com/inspect-js/is-negative-zero/commit/cffe3fc17c6bfaa6996bf7402446b160631a04b3)
- Update `tape`, `jscs` [`3a16616`](https://github.com/inspect-js/is-negative-zero/commit/3a166165ae8770d59c296794d28547379cebec58)
- Use consistent quotes [`9509a81`](https://github.com/inspect-js/is-negative-zero/commit/9509a8110027b12c5762ba48d03b649a921847d5)
- Test on `io.js` `v2.4` [`a9150a3`](https://github.com/inspect-js/is-negative-zero/commit/a9150a3397db339d992e9e4e6ddb52d1237fd617)
- Test on `io.js` `v2.3` [`36d7acf`](https://github.com/inspect-js/is-negative-zero/commit/36d7acf5bb9193a2b35585e2c49ae8be9f45a55e)
- Lock covert to v1.0.0. [`29d8917`](https://github.com/inspect-js/is-negative-zero/commit/29d89171c3aad69ace372edbf641ec3a5468c760)
- Updating jscs [`fe09c8a`](https://github.com/inspect-js/is-negative-zero/commit/fe09c8a6d16c637ecd83a9a8a7f6192faef0754a)
- Updating jscs [`5877bc7`](https://github.com/inspect-js/is-negative-zero/commit/5877bc7c2ed44c1ecc5d31e1c484c523450722d8)
- Running linter as part of tests [`9e77756`](https://github.com/inspect-js/is-negative-zero/commit/9e777563905f511d915ec7257e2637811b911bd6)
- Updating covert [`520a695`](https://github.com/inspect-js/is-negative-zero/commit/520a695164465b88c76860a638baafd4192bce5c)

## [v1.0.0](https://github.com/inspect-js/is-negative-zero/compare/v0.1.1...v1.0.0) - 2014-08-08

### Commits

- Updating tape [`31d1942`](https://github.com/inspect-js/is-negative-zero/commit/31d19422ecd9d453677851a9d5a8d9372a16fb39)
- Updating tape [`e7143bf`](https://github.com/inspect-js/is-negative-zero/commit/e7143bf3b67d8881b1b6ee0444637647d7bb1d2b)

## [v0.1.1](https://github.com/inspect-js/is-negative-zero/compare/v0.1.0...v0.1.1) - 2014-05-13

### Merged

- Simplify code [`#1`](https://github.com/inspect-js/is-negative-zero/pull/1)

### Commits

- Adding a trailing newline [`61fb37f`](https://github.com/inspect-js/is-negative-zero/commit/61fb37f677e871cca53d9309e765d808ddddfd5a)

## [v0.1.0](https://github.com/inspect-js/is-negative-zero/compare/v0.0.0...v0.1.0) - 2014-05-13

### Commits

- Make sure old and unstable nodes don't break Travis [`f627215`](https://github.com/inspect-js/is-negative-zero/commit/f627215527a95dc1ca014600650e00f15fe122c5)
- Updating deps [`b502f48`](https://github.com/inspect-js/is-negative-zero/commit/b502f48e807d7671cb07e2ca247ae2daa62e4165)
- Oops, negative numbers were negative zero! [`746cb97`](https://github.com/inspect-js/is-negative-zero/commit/746cb975d82c0fa0c5058e8e031807f9afcfd6db)
- Updating covert [`99ef4ed`](https://github.com/inspect-js/is-negative-zero/commit/99ef4ed97d2f76f2a5afbef029bf794f1b5bcffa)
- Updating tape [`ee9cfc2`](https://github.com/inspect-js/is-negative-zero/commit/ee9cfc2fd0039bdb65b6493ce0b8e47d18aa17cd)
- Testing on node 0.6 again [`6a9bf0a`](https://github.com/inspect-js/is-negative-zero/commit/6a9bf0a09e210cca09c7a8a225d08ef1e6789b5a)

## v0.0.0 - 2014-01-19

### Commits

- package.json [`8411d92`](https://github.com/inspect-js/is-negative-zero/commit/8411d92ec787fd522a1b5e65154ae88e9024a23f)
- read me [`5c8bf3c`](https://github.com/inspect-js/is-negative-zero/commit/5c8bf3ce4867dbf2997ef01ea6b712aa294ec959)
- Initial commit [`c06f7dc`](https://github.com/inspect-js/is-negative-zero/commit/c06f7dcf926f5b35ba678787a0f16cdd7b544054)
- Tests. [`5c554d4`](https://github.com/inspect-js/is-negative-zero/commit/5c554d405bfb323a7413fde395d8dc39c5316356)
- Travis CI [`334d000`](https://github.com/inspect-js/is-negative-zero/commit/334d000941fc926493cc7dbdb4e5f7ae481a311b)
- Implementation. [`4ef4491`](https://github.com/inspect-js/is-negative-zero/commit/4ef449189c36d471d283e40aa20a8ebfa4985882)
